%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1035293965572505626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************6133036}
  - component: {fileID: 2823097797181665874}
  - component: {fileID: 3828940938033765555}
  - component: {fileID: 6291582697162504314}
  m_Layer: 5
  m_Name: LevelSelect BranchCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &************6133036
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035293965572505626}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.001, y: 0.001, z: 0.001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4357932155116215657}
  - {fileID: 4039593694547975770}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 2.2479858, y: -12.694458}
  m_SizeDelta: {x: 1536, y: 5136}
  m_Pivot: {x: 0.5, y: 0.1}
--- !u!223 &2823097797181665874
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035293965572505626}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 1
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &3828940938033765555
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035293965572505626}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &6291582697162504314
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035293965572505626}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 247
--- !u!1 &5598920187172729576
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4039593694547975770}
  - component: {fileID: 6338078456935099938}
  - component: {fileID: 7804484383545084079}
  - component: {fileID: 7671631388616741422}
  - component: {fileID: 8480264712710089377}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &4039593694547975770
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598920187172729576}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 8.5042, y: 8.5042, z: 8.5042}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: ************6133036}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0.002319336, y: -0.025634766}
  m_SizeDelta: {x: 180.62, y: 603.94}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6338078456935099938
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598920187172729576}
  m_CullTransparentMesh: 1
--- !u!114 &7804484383545084079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598920187172729576}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.078431375}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 06e7fd4935bcb144597872294e1a4478, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &7671631388616741422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598920187172729576}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 938fce054c42f40a3969e27a88d9bdd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ToneFilter: 0
  m_ToneIntensity: 1
  m_ToneParams: {x: 0, y: 0, z: 0, w: 0}
  m_ColorFilter: 0
  m_ColorIntensity: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_ColorGlow: 0
  m_SamplingFilter: 0
  m_SamplingIntensity: 0.5
  m_SamplingWidth: 1
  m_SamplingScale: 1
  m_TransitionFilter: 8
  m_TransitionRate: 0.5
  m_TransitionReverse: 0
  m_TransitionTex: {fileID: 2800000, guid: 260548128f329e14e95573edd7ff2bac, type: 3}
  m_TransitionTexScale: {x: 1, y: 1}
  m_TransitionTexOffset: {x: 0, y: 0}
  m_TransitionTexSpeed: {x: 2.67, y: 0}
  m_TransitionRotation: 90
  m_TransitionKeepAspectRatio: 1
  m_TransitionWidth: 0.894
  m_TransitionSoftness: 0.2
  m_TransitionRange:
    m_Min: 0
    m_Max: 1
  m_TransitionColorFilter: 6
  m_TransitionColor: {r: 0.7738964, g: 0.8773585, b: 0.78241944, a: 1}
  m_TransitionColorGlow: 0
  m_TransitionPatternReverse: 0
  m_TransitionAutoPlaySpeed: 0
  m_TargetMode: 0
  m_TargetColor: {r: 1, g: 1, b: 1, a: 1}
  m_TargetRange: 0.1
  m_TargetSoftness: 0.5
  m_BlendType: 1
  m_SrcBlendMode: 1
  m_DstBlendMode: 10
  m_ShadowMode: 0
  m_ShadowDistance: {x: 1, y: -1}
  m_ShadowIteration: 1
  m_ShadowFade: 0.9
  m_ShadowMirrorScale: 0.5
  m_ShadowBlurIntensity: 1
  m_ShadowColorFilter: 4
  m_ShadowColor: {r: 1, g: 1, b: 1, a: 1}
  m_ShadowColorGlow: 0
  m_GradationMode: 0
  m_GradationIntensity: 1
  m_GradationColorFilter: 1
  m_GradationColor1: {r: 1, g: 1, b: 1, a: 1}
  m_GradationColor2: {r: 1, g: 1, b: 1, a: 1}
  m_GradationColor3: {r: 1, g: 1, b: 1, a: 1}
  m_GradationColor4: {r: 1, g: 1, b: 1, a: 1}
  m_GradationGradient:
    serializedVersion: 2
    key0: {r: 1, g: 1, b: 1, a: 1}
    key1: {r: 1, g: 1, b: 1, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_GradationOffset: 0
  m_GradationScale: 1
  m_GradationRotation: 0
  m_AllowToModifyMeshShape: 1
  m_EdgeMode: 0
  m_EdgeWidth: 0.5
  m_EdgeColorFilter: 4
  m_EdgeColor: {r: 1, g: 1, b: 1, a: 1}
  m_EdgeColorGlow: 0
  m_EdgeShinyRate: 0.5
  m_EdgeShinyWidth: 0.5
  m_EdgeShinyAutoPlaySpeed: 1
  m_PatternArea: 1
  m_DetailFilter: 0
  m_DetailIntensity: 1
  m_DetailThreshold:
    m_Min: 0
    m_Max: 1
  m_DetailTex: {fileID: 0}
  m_DetailTexScale: {x: 1, y: 1}
  m_DetailTexOffset: {x: 0, y: 0}
  m_DetailTexSpeed: {x: 0, y: 0}
  m_CustomRoot: {fileID: 0}
  m_Flip: 0
--- !u!114 &8480264712710089377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598920187172729576}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ee5613baa22c9b4a9caab24cd8399bf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6294227668736252267
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4357932155116215657}
  - component: {fileID: 3964164303971148580}
  - component: {fileID: 2202166635020591950}
  m_Layer: 5
  m_Name: Arrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &4357932155116215657
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6294227668736252267}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 15.319, y: 15.319, z: 15.319}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: ************6133036}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3964164303971148580
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6294227668736252267}
  m_CullTransparentMesh: 1
--- !u!114 &2202166635020591950
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6294227668736252267}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 301fe569da9047c4988d46c1707ea4e5, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
