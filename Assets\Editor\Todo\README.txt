Todo - A Todo List for Unity

* ’Todo' is a no frills todo list app, ideal for small prototypes, game jams, and personal projects. 

* Open the editor window with the shortcut Ctrl-L/Command-L, or through the Window drop down menu.

* Create tasks in the Task Creation area, found at the bottom of the editor window, and assign them to a category.

* The task will then appear in the Task Display area above. Use the drop down category filter to isolate tasks from a specific category.

* Click on an individual task to edit the task description, click on the right-side drop down category menu to change its category, or click on the checkbox to complete a task.

* Completed tasks will only show up in the “All Tasks” category selection, and can be deleted permanently by hitting their respective ‘x’ deletion buttons.

* Components:
** There are only two scripts:
** Assets/Editor/Todo/TodoList.cs - the editor script.
** Assets/Editor/Todo/ListData.cs - the list data structure.

** Data is saved as a TodoList.asset file in Assets/Resources/Todo/TodoList.asset.

* Comments and suggestions for additional features are more than welcome. Send anything <NAME_EMAIL>

****
This is a simple piece of todo software created by <PERSON> of Cipher Prime.
http://www.cipherprime.com

